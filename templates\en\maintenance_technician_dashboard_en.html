<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Technician Dashboard - Khalaifat Company</title>

  <!-- Poppins & Open Sans fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans&family=Poppins:wght@400;700&display=swap" rel="stylesheet" />

  <!-- Chart.js library -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

  <style>
    body {
      font-family: 'Open Sans', sans-serif;
      background-color: #f9fafc;
      margin: 0;
      padding: 0;
      direction: ltr;
      color: #263b58;
      min-height: 100vh;
      display: flex;
      overflow: hidden;
    }

    /* Sidebar */
    .sidebar {
      width: 220px;
      background-color: #263b58;
      color: white;
      display: flex;
      flex-direction: column;
      padding-top: 20px;
      box-sizing: border-box;
      min-height: 100vh;
    }

    .sidebar .logo {
      text-align: center;
      margin-bottom: 30px;
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      font-size: 1.5rem;
      letter-spacing: 1px;
      cursor: default;
      user-select: none;
    }

    .sidebar nav {
      flex: 1;
    }

    .sidebar nav button {
      width: 100%;
      background: none;
      border: none;
      color: white;
      padding: 15px 20px;
      text-align: left;
      font-family: 'Poppins', sans-serif;
      font-weight: 600;
      font-size: 1rem;
      cursor: pointer;
      border-left: 4px solid transparent;
      transition: background-color 0.3s ease, border-color 0.3s ease;
    }

    .sidebar nav button:hover {
      background-color: #1b2c45;
      border-left-color: #dda986;
    }

    .logout-btn {
      background-color: #464646;
      color: white;
      border: none;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      font-size: 1rem;
      width: 90%;
      margin: 20px auto;
      transition: background-color 0.3s ease;
    }

    .logout-btn:hover {
      background-color: #263b58;
    }

    /* Main content */
    main {
      flex: 1;
      padding: 20px 40px;
      overflow-y: auto;
    }

    header {
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      font-size: 1.8rem;
      margin-bottom: 30px;
      color: #263b58;
    }

    .welcome {
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      font-size: 1.4rem;
      margin-bottom: 25px;
      color: #263b58;
    }

    .cards {
      display: flex;
      gap: 20px;
      flex-wrap: wrap;
      justify-content: center;
      margin-bottom: 40px;
    }

    .card {
      background: #263b58;
      flex: 1 1 220px;
      min-width: 220px;
      padding: 25px;
      border-radius: 10px;
      box-shadow: 0 3px 8px rgba(38, 59, 88, 0.3);
      text-align: center;
      color: white;
    }

    .card h3 {
      margin: 0 0 10px 0;
      font-family: 'Poppins', sans-serif;
    }

    .card p {
      font-size: 2rem;
      font-weight: 700;
      margin: 0;
    }

    .progress-chart-container {
      max-width: 320px;
      margin: 0 auto 40px auto;
      background: white;
      border-radius: 10px;
      padding: 20px;
      box-shadow: 0 3px 10px rgba(38,59,88,0.1);
      text-align: center;
    }

    .progress-chart-container h3 {
      font-family: 'Poppins', sans-serif;
      margin-bottom: 20px;
      color: #263b58;
    }

    .tasks-section, .reports-section {
      margin-bottom: 30px;
    }

    .tasks-section h2, .reports-section h2 {
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      margin-bottom: 20px;
      color: #263b58;
      text-align: center;
    }

    ul.tasks-list {
      list-style: none;
      padding: 0;
      max-width: 600px;
      margin: 0 auto;
    }

    ul.tasks-list li {
      background: white;
      margin-bottom: 12px;
      padding: 15px 20px;
      border-radius: 8px;
      box-shadow: 0 1px 6px rgba(0,0,0,0.1);
      font-family: 'Open Sans', sans-serif;
      color: #263b58;
      cursor: default;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
      max-width: 900px;
      margin-left: auto;
      margin-right: auto;
    }

    th, td {
      border: 1px solid #ddd;
      padding: 10px;
      text-align: center;
      font-family: 'Open Sans', sans-serif;
    }

    th {
      background-color: #263b58;
      color: white;
      font-weight: 700;
    }

    .no-tasks, .no-reports {
      font-style: italic;
      color: #888;
      text-align: center;
      margin-top: 20px;
    }
  </style>
</head>
<body>

  <div class="sidebar">
      <img src="K-transparent-black.png" alt="Khalaifat Logo" />
    <nav>
      <button onclick="location.href='technician_dashboard.html'">Home</button>
      <button onclick="location.href='add_technician_report.html'">Add Technician Report</button>
      <button onclick="location.href='my_technician_reports.html'">My Maintenance Reports</button>
      <button onclick="location.href='profile.html'">My Profile</button>
    </nav>
    <button class="logout-btn" onclick="alert('Logged out (temporary)')">Logout</button>
  </div>

  <main>
    <header>Technician Dashboard - Khalaifat Company</header>

    <div class="welcome">Welcome, Fahad</div>

    <div class="cards">
      <div class="card">
        <h3>Open Tasks</h3>
        <p id="openTasksCount">0</p>
      </div>
      <div class="card">
        <h3>Completed Tasks</h3>
        <p id="completedTasksCount">0</p>
      </div>
      <div class="card">
        <h3>Monthly Progress</h3>
        <p id="monthlyProgress">0%</p>
      </div>
    </div>

    <div class="progress-chart-container">
      <h3>Remaining to Achieve Monthly Goal</h3>
      <canvas id="progressChart" width="200" height="200"></canvas>
    </div>

    <section class="tasks-section">
      <h2>Current Tasks</h2>
      <ul id="tasksList" class="tasks-list"></ul>
      <p id="noTasksMessage" class="no-tasks">No tasks available.</p>
    </section>

    <section class="reports-section">
      <h2>My Maintenance Reports</h2>

      <table>
        <thead>
          <tr>
            <th>Date</th>
            <th>Customer Name</th>
            <th>Product Model</th>
            <th>Problem Description</th>
            <th>Status</th>
          </tr>
        </thead>
        <tbody id="reportsTableBody"></tbody>
      </table>
      <p id="noReportsMessage" class="no-reports">No reports available.</p>
    </section>
  </main>

  <script>
    // Sample data for open tasks
    const tasks = [
      "Repairing hood at customer 123",
      "Installing dishwasher at Gudaibiya showroom",
      "Checking electric oven - Service Center",
    ];

    // Sample data for maintenance reports
    const reports = [
      { date: "2025-05-20", customer: "Ahmed Mohamed", model: "KGN57VL20M", problem: "Hood not working", status: "Completed" },
      { date: "2025-05-21", customer: "Sara Ali", model: "MSM6700GB", problem: "Dishwasher leaking water", status: "In Progress" },
      { date: "2025-05-22", customer: "Khalid Hassan", model: "MMB33P5BGB", problem: "Oven not heating", status: "Completed" },
    ];

    const openTasksCount = tasks.length;
    const completedTasksCount = reports.filter(r => r.status === "Completed").length;
    const monthlyGoal = 20; // Monthly target of completed tasks
    const monthlyProgressValue = (completedTasksCount / monthlyGoal) * 100;
    const remainingPercent = Math.max(0, 100 - monthlyProgressValue);

    document.getElementById('openTasksCount').textContent = openTasksCount;
    document.getElementById('completedTasksCount').textContent = completedTasksCount;
    document.getElementById('monthlyProgress').textContent = monthlyProgressValue.toFixed(1) + "%";

    // Load current tasks
    const tasksList = document.getElementById('tasksList');
    const noTasksMessage = document.getElementById('noTasksMessage');

    if(tasks.length === 0) {
      noTasksMessage.style.display = 'block';
    } else {
      noTasksMessage.style.display = 'none';
      tasks.forEach(task => {
        const li = document.createElement('li');
        li.textContent = task;
        tasksList.appendChild(li);
      });
    }

    // Load maintenance reports
    const reportsTableBody = document.getElementById('reportsTableBody');
    const noReportsMessage = document.getElementById('noReportsMessage');

    if(reports.length === 0) {
      noReportsMessage.style.display = 'block';
    } else {
      noReportsMessage.style.display = 'none';
      reports.forEach(report => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
          <td>${report.date}</td>
          <td>${report.customer}</td>
          <td>${report.model}</td>
          <td>${report.problem}</td>
          <td>${report.status}</td>
        `;
        reportsTableBody.appendChild(tr);
      });
    }

    // Progress donut chart for monthly completion
    const ctx = document.getElementById('progressChart').getContext('2d');
    const progressChart = new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: ['Completed', 'Remaining'],
        datasets: [{
          data: [monthlyProgressValue, remainingPercent],
          backgroundColor: ['#263b58', '#dda986'],
          borderWidth: 0
        }]
      },
      options: {
        cutout: '70%',
        responsive: true,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              font: { family: 'Poppins', weight: '700' },
              color: '#263b58'
            }
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                return context.label + ': ' + context.parsed.toFixed(1) + '%';
              }
            }
          }
        }
      }
    });
  </script>

</body>
</html>

<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Technician Report Registration - Khalaifat Company</title>

  <!-- Poppins & Open Sans fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans&family=Poppins:wght@400;700&display=swap" rel="stylesheet" />

  <style>
    /* General page styles */
    body {
      font-family: 'Open Sans', sans-serif;
      background-color: #f9fafc;
      margin: 0;
      padding: 0;
      direction: ltr;
      color: #263b58;
      min-height: 100vh;
      display: flex;
      overflow: hidden;
    }

    /* Sidebar styles */
    .sidebar {
      width: 220px;
      background-color: #263b58;
      color: white;
      display: flex;
      flex-direction: column;
      padding-top: 20px;
      box-sizing: border-box;
      min-height: 100vh;
    }

    .sidebar .logo {
      text-align: center;
      margin-bottom: 30px;
      user-select: none;
    }

    /* الشعار */
    .sidebar .logo img {
      max-width: 140px;
      height: auto;
      display: inline-block;
      margin: 0 auto 30px auto;
      user-select: none;
    }

    .sidebar nav {
      flex: 1;
    }

    .sidebar nav button {
      width: 100%;
      background: none;
      border: none;
      color: white;
      padding: 15px 20px;
      text-align: left;
      font-family: 'Poppins', sans-serif;
      font-weight: 600;
      font-size: 1rem;
      cursor: pointer;
      border-left: 4px solid transparent;
      transition: background-color 0.3s ease, border-color 0.3s ease;
    }

    .sidebar nav button:hover {
      background-color: #1b2c45;
      border-left-color: #dda986;
    }

    .logout-btn {
      background-color: #464646;
      color: white;
      border: none;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      font-size: 1rem;
      width: 90%;
      margin: 20px auto;
      transition: background-color 0.3s ease;
    }

    .logout-btn:hover {
      background-color: #263b58;
    }

    /* Main content styles */
    main {
      flex: 1;
      padding: 30px 50px;
      overflow-y: auto;
      background: white;
      box-sizing: border-box;
    }

    header {
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      font-size: 1.8rem;
      margin-bottom: 30px;
      color: #263b58;
      text-align: center;
    }

    form {
      max-width: 600px;
      margin: 0 auto;
      font-family: 'Open Sans', sans-serif;
    }

    label {
      display: block;
      margin-bottom: 8px;
      margin-top: 20px;
      font-weight: 600;
      font-size: 1rem;
      color: #263b58;
    }

    input[type="text"],
    input[type="date"],
    select,
    textarea {
      width: 100%;
      padding: 12px 15px;
      font-size: 1rem;
      border-radius: 8px;
      border: 1px solid #ccc;
      box-sizing: border-box;
      font-family: 'Open Sans', sans-serif;
      resize: vertical;
      transition: border-color 0.3s ease;
    }

    input[type="text"]:focus,
    input[type="date"]:focus,
    select:focus,
    textarea:focus {
      border-color: #263b58;
      outline: none;
    }

    textarea {
      min-height: 100px;
    }

    input[type="file"] {
      margin-top: 8px;
      font-family: 'Open Sans', sans-serif;
    }

    /* Submit button */
    button.submit-btn {
      margin-top: 30px;
      width: 100%;
      padding: 14px;
      background-color: #263b58;
      color: white;
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      font-size: 1.1rem;
      border: none;
      border-radius: 10px;
      cursor: pointer;
      transition: background-color 0.3s ease;
    }

    button.submit-btn:hover {
      background-color: #1b2c45;
    }

    /* Image preview container */
    .image-preview-container {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      margin-top: 15px;
    }

    .image-preview-container img {
      width: 120px;
      height: 90px;
      object-fit: cover;
      border-radius: 8px;
      border: 1px solid #ccc;
    }

    .success-message {
      margin-top: 20px;
      text-align: center;
      color: green;
      font-weight: 700;
      display: none;
    }
  </style>
</head>
<body>

  <div class="sidebar">
    <div class="logo">
      <img src="K-transparent-black.png" alt="Khalaifat Logo" />
    </div>
    <nav>
      <button onclick="location.href='technician_dashboard.html'">Home</button>
      <button onclick="location.href='add_technician_report.html'">Register Technician Report</button>
      <button onclick="location.href='my_technician_reports.html'">My Reports</button>
      <button onclick="location.href='profile.html'">My Profile</button>
    </nav>
    <button class="logout-btn" onclick="alert('Logged out (temporary)')">Logout</button>
  </div>

  <main>
    <header>Register Technician Report - Khalaifat Company</header>

    <form id="technicianReportForm" enctype="multipart/form-data">
      <label for="customerName">Customer Name</label>
      <input type="text" id="customerName" name="customerName" placeholder="Enter customer name" required />

      <label for="productModel">Product Model</label>
      <input type="text" id="productModel" name="productModel" placeholder="Enter product model" required />

      <label for="reportDate">Report Date</label>
      <input type="date" id="reportDate" name="reportDate" required />

      <label for="problemDescription">Problem Description</label>
      <textarea id="problemDescription" name="problemDescription" placeholder="Describe the problem" required></textarea>

      <label for="status">Report Status</label>
      <select id="status" name="status" required>
        <option value="">Select status</option>
        <option value="In Progress">In Progress</option>
        <option value="Completed">Completed</option>
        <option value="Postponed">Postponed</option>
      </select>

      <label for="imageUpload">Add Images</label>
      <input type="file" id="imageUpload" name="imageUpload" accept="image/*" multiple />

      <div class="image-preview-container" id="imagePreviewContainer"></div>

      <button type="submit" class="submit-btn">Register Report</button>

      <p class="success-message" id="successMessage">Report registered successfully!</p>
    </form>
  </main>

  <script>
    const form = document.getElementById('technicianReportForm');
    const successMessage = document.getElementById('successMessage');
    const imageUpload = document.getElementById('imageUpload');
    const imagePreviewContainer = document.getElementById('imagePreviewContainer');

    // Image preview handler
    imageUpload.addEventListener('change', function() {
      imagePreviewContainer.innerHTML = ''; // Clear previous previews
      const files = this.files;

      if (files.length === 0) {
        return;
      }

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const reader = new FileReader();

        reader.onload = function(e) {
          const img = document.createElement('img');
          img.src = e.target.result;
          imagePreviewContainer.appendChild(img);
        }

        reader.readAsDataURL(file);
      }
    });

    form.addEventListener('submit', function(e) {
      e.preventDefault();

      // Here you can add your code to submit the form data to backend (e.g., using fetch)

      successMessage.style.display = 'block';

      setTimeout(() => {
        successMessage.style.display = 'none';
        form.reset();
        imagePreviewContainer.innerHTML = '';
      }, 2500);
    });
  </script>

</body>
</html>

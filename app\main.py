from fastapi import FastAPI
from app.routes import user_routes, product_routes, report_routes

app = FastAPI()

app.include_router(user_routes.router, prefix="/users", tags=["users"])
app.include_router(product_routes.router, prefix="/products", tags=["products"])
app.include_router(report_routes.router, prefix="/reports", tags=["reports"])

@app.get("/")
async def root():
    return {"message": "Welcome to Khalaifat FastAPI MongoDB app"}

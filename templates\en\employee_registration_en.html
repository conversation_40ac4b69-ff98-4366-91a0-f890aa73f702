<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Employee Registration - Khalaifat Company</title>

  <!-- Poppins & Open Sans fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans&family=Poppins:wght@400;700&display=swap" rel="stylesheet" />

  <style>
    body {
      font-family: 'Open Sans', sans-serif;
      background-color: #f9fafc;
      margin: 0;
      padding: 0;
      direction: ltr;
      color: #263b58;
      min-height: 100vh;
      display: flex;
    }
    .sidebar {
      width: 220px;
      background-color: #263b58;
      color: white;
      display: flex;
      flex-direction: column;
      padding-top: 20px;
      box-sizing: border-box;
      min-height: 100vh;
    }
    .logo {
      text-align: center;
      margin-bottom: 30px;
      user-select: none;
    }
    .logo img {
      max-width: 140px;
      height: auto;
      display: inline-block;
    }
    .sidebar nav {
      flex: 1;
    }
    .sidebar nav button {
      width: 100%;
      background: none;
      border: none;
      color: white;
      padding: 15px 20px;
      text-align: left;
      font-family: 'Poppins', sans-serif;
      font-weight: 600;
      font-size: 1rem;
      cursor: pointer;
      border-left: 4px solid transparent;
      transition: background-color 0.3s ease, border-color 0.3s ease;
    }
    .sidebar nav button:hover {
      background-color: #1b2c45;
      border-left-color: #dda986;
    }
    .logout-btn {
      background-color: #464646;
      color: white;
      border: none;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      font-size: 1rem;
      width: 90%;
      margin: 20px auto;
      transition: background-color 0.3s ease;
    }
    .logout-btn:hover {
      background-color: #263b58;
    }
    main {
      flex: 1;
      padding: 30px 50px;
      overflow-y: auto;
      background: white;
      box-sizing: border-box;
    }
    header {
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      font-size: 1.8rem;
      margin-bottom: 30px;
      color: #263b58;
      text-align: center;
    }
    form {
      max-width: 600px;
      margin: 0 auto 40px auto;
      font-family: 'Open Sans', sans-serif;
    }
    label {
      display: block;
      margin-bottom: 8px;
      margin-top: 20px;
      font-weight: 600;
      font-size: 1rem;
      color: #263b58;
    }
    input[type="text"],
    input[type="tel"],
    input[type="password"],
    select {
      width: 100%;
      padding: 12px 15px;
      font-size: 1rem;
      border-radius: 8px;
      border: 1px solid #ccc;
      box-sizing: border-box;
      font-family: 'Open Sans', sans-serif;
      transition: border-color 0.3s ease;
    }
    input[type="text"]:focus,
    input[type="tel"]:focus,
    input[type="password"]:focus,
    select:focus {
      border-color: #263b58;
      outline: none;
    }
    button.submit-btn {
      margin-top: 20px;
      width: 100%;
      padding: 14px;
      background-color: #263b58;
      color: white;
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      font-size: 1.1rem;
      border: none;
      border-radius: 10px;
      cursor: pointer;
      transition: background-color 0.3s ease;
    }
    button.submit-btn:hover {
      background-color: #1b2c45;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
      font-family: 'Open Sans', sans-serif;
      font-size: 1rem;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 10px;
      text-align: center;
      position: relative;
    }
    th {
      background-color: #263b58;
      color: white;
      font-weight: 700;
    }
    .btn-minus {
      color: white;
      background-color: #e74c3c;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      font-weight: 700;
      cursor: pointer;
      user-select: none;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      font-size: 18px;
      position: relative;
    }
    .actions-menu {
      display: none;
      position: absolute;
      top: 30px;
      right: 0;
      background: white;
      border: 1px solid #ccc;
      box-shadow: 0 2px 8px rgba(0,0,0,0.2);
      border-radius: 6px;
      z-index: 10;
      min-width: 140px;
      font-family: 'Open Sans', sans-serif;
    }
    .actions-menu button {
      display: block;
      width: 100%;
      padding: 10px 12px;
      background: none;
      border: none;
      text-align: left;
      cursor: pointer;
      font-size: 0.95rem;
      color: #263b58;
      font-weight: 600;
      transition: background-color 0.2s ease;
    }
    .actions-menu button:hover {
      background-color: #f0f0f0;
    }
    .show-menu {
      display: block;
    }
    #editEmployeeModal {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      border-radius: 10px;
      box-shadow: 0 3px 15px rgba(0,0,0,0.3);
      padding: 25px 30px;
      z-index: 100;
      display: none;
      width: 90%;
      max-width: 500px;
      font-family: 'Open Sans', sans-serif;
    }
    #editEmployeeModal label {
      margin-top: 15px;
      font-weight: 600;
      color: #263b58;
    }
    #editEmployeeModal input,
    #editEmployeeModal select {
      width: 100%;
      padding: 10px 12px;
      border-radius: 6px;
      border: 1px solid #ccc;
      margin-top: 5px;
      box-sizing: border-box;
      font-family: 'Open Sans', sans-serif;
    }
    #editEmployeeModal select {
      height: 38px;
    }
    #editEmployeeModalButtons {
      margin-top: 20px;
      text-align: center;
    }
    #editEmployeeModalButtons button {
      padding: 10px 20px;
      font-weight: 700;
      font-family: 'Poppins', sans-serif;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      margin: 0 10px;
      transition: background-color 0.3s ease;
      font-size: 1rem;
    }
    #saveEditBtn {
      background-color: #263b58;
      color: white;
    }
    #saveEditBtn:hover {
      background-color: #1b2c45;
    }
    #cancelEditBtn {
      background-color: #ccc;
      color: #263b58;
    }
    #cancelEditBtn:hover {
      background-color: #aaa;
    }
    #modalOverlay {
      display: none;
      position: fixed;
      top: 0; left: 0; right: 0; bottom: 0;
      background-color: rgba(0,0,0,0.4);
      z-index: 90;
    }
    #confirmDeleteModal {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      border-radius: 10px;
      box-shadow: 0 3px 15px rgba(0,0,0,0.3);
      padding: 25px 30px;
      z-index: 110;
      display: none;
      width: 90%;
      max-width: 400px;
      font-family: 'Open Sans', sans-serif;
      text-align: center;
    }
    #confirmDeleteModal p {
      font-size: 1.1rem;
      color: #263b58;
      margin-bottom: 25px;
      font-weight: 600;
    }
    #confirmDeleteButtons button {
      padding: 10px 25px;
      font-weight: 700;
      font-family: 'Poppins', sans-serif;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      margin: 0 15px;
      transition: background-color 0.3s ease;
      font-size: 1rem;
      min-width: 80px;
    }
    #confirmYesBtn {
      background-color: #e74c3c;
      color: white;
    }
    #confirmYesBtn:hover {
      background-color: #c0392b;
    }
    #confirmNoBtn {
      background-color: #ccc;
      color: #263b58;
    }
    #confirmNoBtn:hover {
      background-color: #aaa;
    }
  </style>
</head>
<body>

  <div class="sidebar">
    <div class="logo">
      <img src="K-transparent-black.png" alt="Khalaifat Logo" />
    </div>
    <nav>
      <button onclick="alert('Home')">Home</button>
      <button onclick="alert('Register Sales Report')">Register Sales Report</button>
      <button onclick="alert('Register Employees')">Register Employees</button>
      <button onclick="alert('Manage Products')">Manage Products</button>
      <button onclick="alert('My Profile')">My Profile</button>
    </nav>
    <button class="logout-btn" onclick="alert('Logged out')">Logout</button>
  </div>

  <main>
    <header>Employee Registration - Khalaifat Company</header>

    <form id="employeeForm">
      <label for="employeeName">Employee Name</label>
      <input type="text" id="employeeName" name="employeeName" placeholder="Enter employee name" required />

      <label for="phoneNumber">Phone Number</label>
      <input type="tel" id="phoneNumber" name="phoneNumber" placeholder="Enter phone number" required />

      <label for="password">Password</label>
      <input type="password" id="password" name="password" placeholder="Enter password" required />

      <label for="role">Role</label>
      <select id="role" name="role" required>
        <option value="">Select role</option>
        <option value="Employee">Employee</option>
        <option value="Manager">Manager</option>
        <option value="Supervisor">Supervisor</option>
      </select>

      <button type="submit" class="submit-btn">Add Employee</button>
    </form>

    <table id="employeesTable" style="margin-top: 40px;">
      <thead>
        <tr>
          <th>Employee Name</th>
          <th>Phone Number</th>
          <th>Password</th>
          <th>Role</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <!-- Added employees appear here -->
      </tbody>
    </table>

    <p class="success-message" id="successMessage">Employee added successfully!</p>
  </main>

  <!-- Edit employee modal -->
  <div id="modalOverlay"></div>
  <div id="editEmployeeModal">
    <h3>Edit Employee Details</h3>
    <form id="editEmployeeForm">
      <label for="editEmployeeName">Employee Name</label>
      <input type="text" id="editEmployeeName" name="editEmployeeName" required />

      <label for="editPhoneNumber">Phone Number</label>
      <input type="tel" id="editPhoneNumber" name="editPhoneNumber" required />

      <label for="editPassword">Password</label>
      <input type="password" id="editPassword" name="editPassword" required />

      <label for="editRole">Role</label>
      <select id="editRole" name="editRole" required>
        <option value="">Select role</option>
        <option value="Employee">Employee</option>
        <option value="Manager">Manager</option>
        <option value="Supervisor">Supervisor</option>
      </select>

      <div id="editEmployeeModalButtons">
        <button type="submit" id="saveEditBtn">Save Changes</button>
        <button type="button" id="cancelEditBtn">Cancel</button>
      </div>
    </form>
  </div>

  <!-- Delete confirmation modal -->
  <div id="confirmDeleteModal">
    <p>Are you sure you want to delete this data?</p>
    <div id="confirmDeleteButtons">
      <button id="confirmYesBtn">Yes</button>
      <button id="confirmNoBtn">No</button>
    </div>
  </div>

  <script>
    const employeeForm = document.getElementById('employeeForm');
    const employeesTableBody = document.querySelector('#employeesTable tbody');
    const successMessage = document.getElementById('successMessage');
    const modalOverlay = document.getElementById('modalOverlay');
    const editModal = document.getElementById('editEmployeeModal');
    const editEmployeeForm = document.getElementById('editEmployeeForm');
    const confirmDeleteModal = document.getElementById('confirmDeleteModal');
    const confirmYesBtn = document.getElementById('confirmYesBtn');
    const confirmNoBtn = document.getElementById('confirmNoBtn');

    let employees = [];
    let editIndex = null;
    let deleteIndex = null;

    function renderEmployees() {
      employeesTableBody.innerHTML = '';

      employees.forEach((emp, index) => {
        const tr = document.createElement('tr');

        tr.innerHTML = `
          <td>${emp.name}</td>
          <td>${emp.phone}</td>
          <td>${emp.password}</td>
          <td>${emp.role}</td>
          <td style="position: relative;">
            <span class="btn-minus" title="Options" data-index="${index}">−</span>
            <div class="actions-menu" id="actionsMenu${index}">
              <button type="button" class="edit-btn" data-index="${index}">Edit</button>
              <button type="button" class="delete-btn" data-index="${index}">Delete</button>
            </div>
          </td>
        `;

        employeesTableBody.appendChild(tr);
      });

      // Bind option buttons
      document.querySelectorAll('.btn-minus').forEach(btn => {
        btn.onclick = (e) => {
          const idx = e.target.dataset.index;
          toggleMenu(idx);
        };
      });

      document.querySelectorAll('.edit-btn').forEach(btn => {
        btn.onclick = (e) => {
          const idx = e.target.dataset.index;
          openEditModal(idx);
        };
      });

      document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.onclick = (e) => {
          const idx = e.target.dataset.index;
          openDeleteConfirm(idx);
        };
      });
    }

    function toggleMenu(index) {
      document.querySelectorAll('.actions-menu').forEach(menu => {
        if(menu.id !== 'actionsMenu'+index) {
          menu.classList.remove('show-menu');
        }
      });

      const menu = document.getElementById('actionsMenu' + index);
      menu.classList.toggle('show-menu');
    }

    function closeAllMenus() {
      document.querySelectorAll('.actions-menu').forEach(menu => {
        menu.classList.remove('show-menu');
      });
    }

    document.addEventListener('click', (e) => {
      if (!e.target.classList.contains('btn-minus') && !e.target.closest('.actions-menu')) {
        closeAllMenus();
      }
    });

    employeeForm.addEventListener('submit', (e) => {
      e.preventDefault();

      const name = employeeForm.employeeName.value.trim();
      const phone = employeeForm.phoneNumber.value.trim();
      const password = employeeForm.password.value;
      const role = employeeForm.role.value;

      if(name && phone && password && role) {
        employees.push({name, phone, password, role});
        renderEmployees();
        employeeForm.reset();
        successMessage.style.display = 'block';

        setTimeout(() => {
          successMessage.style.display = 'none';
        }, 2500);
      }
    });

    function openEditModal(index) {
      editIndex = index;
      const emp = employees[index];

      editEmployeeForm.editEmployeeName.value = emp.name;
      editEmployeeForm.editPhoneNumber.value = emp.phone;
      editEmployeeForm.editPassword.value = emp.password;
      editEmployeeForm.editRole.value = emp.role;

      editModal.style.display = 'block';
      modalOverlay.style.display = 'block';
      closeAllMenus();
    }

    function closeEditModal() {
      editModal.style.display = 'none';
      modalOverlay.style.display = 'none';
      editIndex = null;
    }

    editEmployeeForm.addEventListener('submit', (e) => {
      e.preventDefault();

      if(editIndex !== null) {
        employees[editIndex].name = editEmployeeForm.editEmployeeName.value.trim();
        employees[editIndex].phone = editEmployeeForm.editPhoneNumber.value.trim();
        employees[editIndex].password = editEmployeeForm.editPassword.value;
        employees[editIndex].role = editEmployeeForm.editRole.value;

        renderEmployees();
        closeEditModal();
      }
    });

    document.getElementById('cancelEditBtn').addEventListener('click', () => {
      closeEditModal();
    });

    function openDeleteConfirm(index) {
      deleteIndex = index;
      confirmDeleteModal.style.display = 'block';
      modalOverlay.style.display = 'block';
      closeAllMenus();
    }

    function closeDeleteConfirm() {
      confirmDeleteModal.style.display = 'none';
      modalOverlay.style.display = 'none';
      deleteIndex = null;
    }

    confirmYesBtn.addEventListener('click', () => {
      if(deleteIndex !== null) {
        employees.splice(deleteIndex, 1);
        renderEmployees();
        closeDeleteConfirm();
      }
    });

    confirmNoBtn.addEventListener('click', () => {
      closeDeleteConfirm();
    });
  </script>

</body>
</html>

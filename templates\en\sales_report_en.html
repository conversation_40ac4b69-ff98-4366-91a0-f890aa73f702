<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Multi-Product Sales Report Registration - Khalaifat Company</title>

  <!-- Poppins & Open Sans fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans&family=Poppins:wght@400;700&display=swap" rel="stylesheet" />

  <style>
    body {
      font-family: 'Open Sans', sans-serif;
      background-color: #f9fafc;
      margin: 0;
      padding: 0;
      direction: ltr;
      color: #263b58;
      min-height: 100vh;
      display: flex;
    }

    /* Sidebar */
    .sidebar {
      width: 220px;
      background-color: #263b58;
      color: white;
      display: flex;
      flex-direction: column;
      padding-top: 20px;
      box-sizing: border-box;
      min-height: 100vh;
    }

    .sidebar .logo {
      text-align: center;
      margin-bottom: 30px;
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      font-size: 1.5rem;
      letter-spacing: 1px;
      user-select: none;
    }

    .sidebar nav {
      flex: 1;
    }

    .sidebar nav button {
      width: 100%;
      background: none;
      border: none;
      color: white;
      padding: 15px 20px;
      text-align: left;
      font-family: 'Poppins', sans-serif;
      font-weight: 600;
      font-size: 1rem;
      cursor: pointer;
      border-left: 4px solid transparent;
      transition: background-color 0.3s ease, border-color 0.3s ease;
    }

    .sidebar nav button:hover {
      background-color: #1b2c45;
      border-left-color: #dda986;
    }

    .logout-btn {
      background-color: #464646;
      color: white;
      border: none;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      font-size: 1rem;
      width: 90%;
      margin: 20px auto;
      transition: background-color 0.3s ease;
    }

    .logout-btn:hover {
      background-color: #263b58;
    }

    /* Main content */
    main {
      flex: 1;
      padding: 30px 50px;
      overflow-y: auto;
      background: white;
      box-sizing: border-box;
    }

    header {
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      font-size: 1.8rem;
      margin-bottom: 30px;
      color: #263b58;
      text-align: center;
    }

    form {
      max-width: 700px;
      margin: 0 auto;
      font-family: 'Open Sans', sans-serif;
    }

    label {
      display: block;
      margin-bottom: 8px;
      margin-top: 20px;
      font-weight: 600;
      font-size: 1rem;
      color: #263b58;
    }

    input[type="text"],
    input[type="number"],
    input[type="date"] {
      width: 100%;
      padding: 12px 15px;
      font-size: 1rem;
      border-radius: 8px;
      border: 1px solid #ccc;
      box-sizing: border-box;
      font-family: 'Open Sans', sans-serif;
      transition: border-color 0.3s ease;
    }

    input[type="text"]:focus,
    input[type="number"]:focus,
    input[type="date"]:focus {
      border-color: #263b58;
      outline: none;
    }

    /* Add product button */
    button.add-product-btn {
      margin-top: 15px;
      background-color: #dda986;
      color: #263b58;
      border: none;
      padding: 10px 18px;
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      border-radius: 8px;
      cursor: pointer;
      transition: background-color 0.3s ease;
    }

    button.add-product-btn:hover {
      background-color: #c99f7d;
    }

    /* Submit button */
    button.submit-btn {
      margin-top: 30px;
      width: 100%;
      padding: 14px;
      background-color: #263b58;
      color: white;
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      font-size: 1.1rem;
      border: none;
      border-radius: 10px;
      cursor: pointer;
      transition: background-color 0.3s ease;
    }

    button.submit-btn:hover {
      background-color: #1b2c45;
    }

    /* Products container */
    .products-container {
      margin-top: 20px;
      border: 1px solid #ccc;
      border-radius: 10px;
      padding: 15px;
      background-color: #f5f5f5;
    }

    .product-item {
      display: grid;
      grid-template-columns: 2fr 1fr 1fr 1fr 0.5fr;
      gap: 12px;
      align-items: center;
      margin-bottom: 12px;
    }

    .product-item label {
      margin: 0;
      font-weight: 600;
      font-size: 0.9rem;
      color: #555;
    }

    .product-item input {
      padding: 8px 10px;
      font-size: 1rem;
      border-radius: 6px;
      border: 1px solid #ccc;
      box-sizing: border-box;
    }

    .remove-product-btn {
      background-color: #e74c3c;
      border: none;
      color: white;
      font-weight: 700;
      border-radius: 6px;
      cursor: pointer;
      padding: 8px 12px;
      transition: background-color 0.3s ease;
    }

    .remove-product-btn:hover {
      background-color: #c0392b;
    }

    .success-message {
      margin-top: 20px;
      text-align: center;
      color: green;
      font-weight: 700;
      display: none;
    }
  </style>
</head>
<body>

  <div class="sidebar">
    <div class="logo">Khalaifat</div>
    <nav>
      <button onclick="alert('Home')">Home</button>
      <button onclick="alert('Register Sales Report')">Register Sales Report</button>
      <button onclick="alert('My Reports')">My Reports</button>
      <button onclick="alert('My Profile')">My Profile</button>
    </nav>
    <button class="logout-btn" onclick="alert('Logged out')">Logout</button>
  </div>

  <main>
    <header>Multi-Product Sales Report Registration - Khalaifat Company</header>

    <form id="salesReportForm">
      <label for="reportDate">Report Date</label>
      <input type="date" id="reportDate" name="reportDate" required />

      <div class="products-container" id="productsContainer"></div>

      <button type="button" class="add-product-btn" id="addProductBtn">Add New Product +</button>

      <button type="submit" class="submit-btn">Submit Report</button>

      <p class="success-message" id="successMessage">Report registered successfully!</p>
    </form>
  </main>

  <script>
    const productsContainer = document.getElementById('productsContainer');
    const addProductBtn = document.getElementById('addProductBtn');
    const form = document.getElementById('salesReportForm');
    const successMessage = document.getElementById('successMessage');

    function createProductItem(index) {
      const div = document.createElement('div');
      div.className = 'product-item';
      div.dataset.index = index;

      div.innerHTML = `
        <input type="text" name="productModel_${index}" placeholder="Product Model" required />
        <input type="number" name="quantity_${index}" placeholder="Quantity" min="1" required />
        <input type="number" step="0.001" name="unitPrice_${index}" placeholder="Unit Price (BHD)" min="0" required />
        <input type="number" step="0.001" name="totalPrice_${index}" placeholder="Total (BHD)" min="0" required />
        <button type="button" class="remove-product-btn" title="Remove Product">&times;</button>
      `;

      div.querySelector('.remove-product-btn').addEventListener('click', () => {
        productsContainer.removeChild(div);
      });

      return div;
    }

    let productIndex = 0;
    productsContainer.appendChild(createProductItem(productIndex));
    productIndex++;

    addProductBtn.addEventListener('click', () => {
      productsContainer.appendChild(createProductItem(productIndex));
      productIndex++;
    });

    form.addEventListener('submit', (e) => {
      e.preventDefault();

      // Add your backend submission code here (e.g., fetch API)

      successMessage.style.display = 'block';

      setTimeout(() => {
        successMessage.style.display = 'none';
        form.reset();

        productsContainer.innerHTML = '';
        productIndex = 0;
        productsContainer.appendChild(createProductItem(productIndex));
        productIndex++;
      }, 2500);
    });
  </script>

</body>
</html>

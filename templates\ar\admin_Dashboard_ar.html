<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>لوحة تحكم المدير - شركة خليفات</title>

  <!-- خطوط Poppins و Open Sans -->
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans&family=Poppins:wght@400;700&display=swap" rel="stylesheet" />

  <!-- مكتبة Chart.js -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

  <style>
    body {
      font-family: 'Open Sans', sans-serif;
      background-color: #f9fafc;
      margin: 0;
      padding: 0;
      direction: rtl;
      color: #263b58;
      display: flex;
      height: 100vh;
      overflow: hidden;
    }

    /* القائمة الجانبية */
    .sidebar {
      width: 250px;
      background-color: #263b58;
      color: white;
      display: flex;
      flex-direction: column;
      padding-top: 20px;
      box-sizing: border-box;
      transition: width 0.3s ease;
      position: relative;
    }

    .sidebar.collapsed {
      width: 70px;
    }

    .sidebar .logo {
      text-align: center;
      margin-bottom: 30px;
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      font-size: 1.5rem;
      letter-spacing: 1px;
      cursor: default;
      user-select: none;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .sidebar .logo img {
      max-width: 120px;
      height: auto;
      margin-bottom: 8px;
      display: block;
    }

    .sidebar.collapsed .logo {
      font-size: 1.2rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .sidebar nav {
      flex: 1;
    }

    .sidebar nav button {
      width: 100%;
      background: none;
      border: none;
      color: white;
      padding: 15px 20px;
      text-align: right;
      font-family: 'Poppins', sans-serif;
      font-weight: 600;
      font-size: 1rem;
      cursor: pointer;
      border-left: 4px solid transparent;
      transition: background-color 0.3s ease, border-color 0.3s ease;
    }

    .sidebar nav button:hover {
      background-color: #1b2c45;
      border-left-color: #dda986;
    }

    .sidebar nav button:focus {
      outline: none;
      background-color: #1b2c45;
      border-left-color: #dda986;
    }

    /* زر تصغير وتوسيع القائمة */
    .toggle-btn {
      position: absolute;
      top: 10px;
      left: -20px;
      background-color: #263b58;
      border-radius: 50%;
      width: 30px;
      height: 30px;
      border: 2px solid white;
      color: white;
      font-weight: bold;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
      user-select: none;
      transition: transform 0.3s ease;
      z-index: 10;
    }

    .sidebar.collapsed .toggle-btn {
      transform: rotate(180deg);
      left: -15px;
    }

    /* المحتوى الرئيسي */
    .main-content {
      flex: 1;
      overflow-y: auto;
      padding: 20px 40px;
    }

    header {
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      font-size: 1.8rem;
      margin-bottom: 30px;
      color: #263b58;
    }

    .cards {
      display: flex;
      gap: 20px;
      flex-wrap: wrap;
      justify-content: center;
      margin-bottom: 40px;
    }

    .card {
      background: white;
      flex: 1 1 250px;
      min-width: 250px;
      padding: 25px;
      border-radius: 10px;
      box-shadow: 0 3px 8px rgba(38, 59, 88, 0.1);
      text-align: center;
    }

    .card h3 {
      margin: 0 0 10px 0;
      font-family: 'Poppins', sans-serif;
      color: #263b58;
    }

    .card p {
      font-size: 2rem;
      font-weight: 700;
      margin: 0;
      color: #464646;
    }

    .charts {
      display: flex;
      gap: 40px;
      flex-wrap: wrap;
      justify-content: center;
    }

    .chart-container {
      background: white;
      padding: 20px;
      border-radius: 10px;
      box-shadow: 0 3px 8px rgba(38, 59, 88, 0.1);
      flex: 1 1 500px;
      min-width: 300px;
    }

    .chart-container h4 {
      font-family: 'Poppins', sans-serif;
      text-align: center;
      margin-bottom: 15px;
      color: #263b58;
    }

    /* زر تسجيل الخروج */
    .logout-btn {
      background-color: #464646;
      color: white;
      border: none;
      padding: 10px 18px;
      border-radius: 6px;
      cursor: pointer;
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      transition: background-color 0.3s ease;
      margin-bottom: 20px;
      width: 90%;
      align-self: center;
    }

    .logout-btn:hover {
      background-color: #263b58;
    }

  </style>
</head>
<body>

  <div class="sidebar" id="sidebar">
    <div class="logo">
      <img src="K-transparent-black.png" alt="Khalaifat Logo" />
    </div>

    <nav>
      <button onclick="location.href='add_sales_report.html'">إضافة تقرير مبيعات</button>
      <button onclick="location.href='add_technician_report.html'">إضافة تقرير فني</button>
      <button onclick="location.href='view_sales_reports.html'">عرض تقارير المبيعات</button>
      <button onclick="location.href='view_technician_reports.html'">عرض تقارير الفنيين</button>
      <button onclick="location.href='user_management.html'">إدارة المستخدمين</button>
      <button onclick="location.href='settings.html'">الإعدادات</button>
      <button class="logout-btn" onclick="alert('تسجيل الخروج (مؤقت)')">تسجيل خروج</button>
    </nav>
  </div>

  <div class="main-content">
    <header>لوحة تحكم المدير - شركة خليفات</header>

    <div class="cards">
      <div class="card">
        <h3>إجمالي المبيعات اليوم</h3>
        <p id="totalSalesToday">0 د.ب</p>
      </div>
      <div class="card">
        <h3>عدد التقارير اليوم</h3>
        <p id="reportsCount">0</p>
      </div>
      <div class="card">
        <h3>أفضل موظف مبيعات</h3>
        <p id="topSalesperson">-</p>
      </div>
      <div class="card">
        <h3>نسبة الإنجاز الشهري</h3>
        <p id="monthlyProgress">0%</p>
      </div>
    </div>

    <div class="charts">
      <div class="chart-container">
        <h4>مبيعات الشهر الحالي</h4>
        <canvas id="monthlySalesChart"></canvas>
      </div>
      <div class="chart-container">
        <h4>توزيع مبيعات الموظفين</h4>
        <canvas id="salesByEmployeeChart"></canvas>
      </div>
    </div>
  </div>

  <script>
    // بيانات وهمية للتجربة
    const totalSalesToday = 1250.50;
    const reportsCount = 18;
    const topSalesperson = "مهدي";
    const monthlyProgress = 76;

    document.getElementById('totalSalesToday').textContent = totalSalesToday.toFixed(3) + " د.ب";
    document.getElementById('reportsCount').textContent = reportsCount;
    document.getElementById('topSalesperson').textContent = topSalesperson;
    document.getElementById('monthlyProgress').textContent = monthlyProgress + "%";

    // إعداد مخطط مبيعات الشهر
    const monthlySalesCtx = document.getElementById('monthlySalesChart').getContext('2d');
    const monthlySalesChart = new Chart(monthlySalesCtx, {
      type: 'line',
      data: {
        labels: ['الأسبوع 1', 'الأسبوع 2', 'الأسبوع 3', 'الأسبوع 4'],
        datasets: [{
          label: 'مبيعات (د.ب)',
          data: [300, 400, 350, 500],
          borderColor: '#263b58',
          backgroundColor: 'rgba(38, 59, 88, 0.2)',
          fill: true,
          tension: 0.3,
          pointRadius: 5,
          pointHoverRadius: 7,
        }]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            ticks: { stepSize: 100 }
          }
        }
      }
    });

    // إعداد مخطط توزيع مبيعات الموظفين
    const salesByEmployeeCtx = document.getElementById('salesByEmployeeChart').getContext('2d');
    const salesByEmployeeChart = new Chart(salesByEmployeeCtx, {
      type: 'doughnut',
      data: {
        labels: ['مهدي', 'سيد هشام', 'كميل', 'علي'],
        datasets: [{
          label: 'مبيعات',
          data: [40, 25, 20, 15],
          backgroundColor: [
            '#263b58',
            '#4688a1',
            '#9dc8cf',
            '#dda986'
          ]
        }]
      },
      options: {
        responsive: true,
        cutout: '70%',
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              color: '#263b58',
              font: { family: 'Poppins', weight: '700' }
            }
          }
        }
      }
    });
  </script>

</body>
</html>

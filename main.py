from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from bson import ObjectId
from typing import Optional
from motor.motor_asyncio import AsyncIOMotorClient

app = FastAPI()

MONGO_DETAILS = "mongodb+srv://mohdalaraibi1158:<EMAIL>/?retryWrites=true&w=majority&tlsAllowInvalidCertificates=true"

client = AsyncIOMotorClient(MONGO_DETAILS)
db = client.khalaifat_db
users_collection = db.get_collection("users")

@app.get("/")
async def root():
    return {"message": "Welcome to Khalaifat FastAPI MongoDB app"}

class PyObjectId(ObjectId):
    @classmethod
    def __get_validators__(cls):
        yield cls.validate
    
    @classmethod
    def validate(cls, v):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid ObjectId")
        return ObjectId(v)
    
    @classmethod
    def __get_pydantic_json_schema__(cls, core_schema):
        return {
            "type": "string",
            "format": "objectid",
            "pattern": "^[0-9a-fA-F]{24}$"
        }

class UserModel(BaseModel):
    id: Optional[PyObjectId]
    username: str
    password: str
    role: str = "employee"

    class Config:
        json_encoders = {ObjectId: str}
        arbitrary_types_allowed = True

class UserCreate(BaseModel):
    username: str
    password: str
    role: str = "employee"

@app.post("/users/", response_model=UserModel)
async def create_user(user: UserCreate):
    user_dict = user.dict()
    existing_user = await users_collection.find_one({"username": user_dict["username"]})
    if existing_user:
        raise HTTPException(status_code=400, detail="Username already exists")
    result = await users_collection.insert_one(user_dict)
    created_user = await users_collection.find_one({"_id": result.inserted_id})
    return created_user
test_mongo.py

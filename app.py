from flask import Flask, render_template, request, redirect, url_for, flash, session
import os
from datetime import datetime
from pymongo.mongo_client import MongoClient
from pymongo.server_api import ServerApi
from bson.objectid import ObjectId

app = Flask(__name__)
app.secret_key = 'khalaifat_app_secret_key'  # مفتاح سري للجلسات والنماذج

# اتصال MongoDB
uri = "mongodb+srv://mohdalaraibi1158:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"
client = MongoClient(uri, server_api=ServerApi('1'))

try:
    client.admin.command('ping')
    print("Pinged your deployment. Successfully connected to MongoDB!")
except Exception as e:
    print("Error connecting to MongoDB:", e)

db = client.get_database()  # اسم قاعدة البيانات الافتراضي من الرابط

# مجموعات (Collections) MongoDB
users_collection = db['users']
products_collection = db['products']
sales_reports_collection = db['sales_reports']
technician_reports_collection = db['technician_reports']

# المسارات (Routes)
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        language = request.form.get('language', 'ar')  # الافتراضي هو العربية

        user = users_collection.find_one({"username": username})

        if user and user.get('password') == password:
            session['user_id'] = str(user['_id'])
            session['username'] = user['username']
            session['role'] = user['role']
            session['language'] = language

            if user['role'] == 'admin':
                return redirect(url_for('admin_dashboard'))
            elif user['role'] == 'employee':
                return redirect(url_for('employee_dashboard'))
            elif user['role'] == 'technician':
                return redirect(url_for('technician_dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة')

    language = request.args.get('language', 'ar')
    return render_template(f'{language}/login_{language}.html')

@app.route('/admin/dashboard')
def admin_dashboard():
    if 'user_id' not in session or session['role'] != 'admin':
        return redirect(url_for('login'))

    language = session.get('language', 'ar')
    return render_template(f'{language}/admin_Dashboard_{language}.html')

@app.route('/employee/dashboard')
def employee_dashboard():
    if 'user_id' not in session or session['role'] != 'employee':
        return redirect(url_for('login'))

    language = session.get('language', 'ar')
    return render_template(f'{language}/employee__Dashboard_{language}.html')

@app.route('/technician/dashboard')
def technician_dashboard():
    if 'user_id' not in session or session['role'] != 'technician':
        return redirect(url_for('login'))

    language = session.get('language', 'ar')
    return render_template(f'{language}/maintenance_technician_dashboard_{language}.html')

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('login'))

# تشغيل التطبيق
if __name__ == '__main__':
    app.run(debug=True)

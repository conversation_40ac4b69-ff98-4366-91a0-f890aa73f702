from pydantic import BaseModel
from bson import ObjectId
from typing import Optional

class PyObjectId(ObjectId):
    @classmethod
    def __get_validators__(cls):
        yield cls.validate
    
    @classmethod
    def validate(cls, v):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid ObjectId")
        return ObjectId(v)
    
    @classmethod
    def __get_pydantic_json_schema__(cls, core_schema):
        return {
            "type": "string",
            "format": "objectid",
            "pattern": "^[0-9a-fA-F]{24}$"
        }

class ProductModel(BaseModel):
    id: Optional[PyObjectId]
    name: str
    description: Optional[str] = None
    price: float

    class Config:
        json_encoders = {ObjectId: str}
        arbitrary_types_allowed = True

class ProductCreate(BaseModel):
    name: str
    description: Optional[str] = None
    price: float

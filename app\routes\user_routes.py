from fastapi import APIRouter, HTTPException
from typing import List
from app.db import users_collection
from app.models.user import UserModel, UserCreate

router = APIRouter()

@router.post("/", response_model=UserModel)
async def create_user(user: UserCreate):
    user_dict = user.dict()
    existing_user = await users_collection.find_one({"username": user_dict["username"]})
    if existing_user:
        raise HTTPException(status_code=400, detail="Username already exists")
    result = await users_collection.insert_one(user_dict)
    created_user = await users_collection.find_one({"_id": result.inserted_id})
    return created_user

@router.get("/", response_model=List[UserModel])
async def get_users():
    users = []
    async for user in users_collection.find():
        users.append(UserModel(**user))
    return users

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>تسجيل الموظفين - شركة خليفات</title>
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans&family=Poppins:wght@400;700&display=swap" rel="stylesheet" />
  <style>
    body { font-family: 'Open Sans', sans-serif; background-color: #f9fafc; margin: 0; padding: 0; direction: rtl; color: #263b58; min-height: 100vh; display: flex; }
    .sidebar { width: 220px; background-color: #263b58; color: white; display: flex; flex-direction: column; padding-top: 20px; box-sizing: border-box; min-height: 100vh; }
    .sidebar .logo { text-align: center; margin-bottom: 20px; }
    .sidebar .logo img { max-width: 140px; height: auto; display: block; margin: 0 auto; }
    .lang-switch { text-align: center; margin-bottom: 24px; }
    .lang-switch button { background: #fff; color: #263b58; border: 1px solid #263b58; border-radius: 8px; font-family: 'Poppins', sans-serif; font-weight: 700; padding: 8px 20px; cursor: pointer; font-size: 1rem; }
    .lang-switch button:hover { background: #263b58; color: #fff; border-color: #dda986; }
    .sidebar nav { flex: 1; }
    .sidebar nav button { width: 100%; background: none; border: none; color: white; padding: 15px 20px; text-align: right; font-family: 'Poppins', sans-serif; font-weight: 600; font-size: 1rem; cursor: pointer; border-left: 4px solid transparent; transition: background-color 0.3s ease, border-color 0.3s ease; }
    .sidebar nav button:hover { background-color: #1b2c45; border-left-color: #dda986; }
    .logout-btn { background-color: #464646; color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; font-family: 'Poppins', sans-serif; font-weight: 700; font-size: 1rem; width: 90%; margin: 20px auto; transition: background-color 0.3s ease; }
    .logout-btn:hover { background-color: #263b58; }
    main { flex: 1; padding: 30px 50px; overflow-y: auto; background: white; box-sizing: border-box; }
    header { font-family: 'Poppins', sans-serif; font-weight: 700; font-size: 1.8rem; margin-bottom: 30px; color: #263b58; text-align: center; }
    form { max-width: 600px; margin: 0 auto 40px auto; font-family: 'Open Sans', sans-serif; }
    label { display: block; margin-bottom: 8px; margin-top: 20px; font-weight: 600; font-size: 1rem; color: #263b58; }
    input[type="text"], input[type="tel"], input[type="password"], select { width: 100%; padding: 12px 15px; font-size: 1rem; border-radius: 8px; border: 1px solid #ccc; box-sizing: border-box; font-family: 'Open Sans', sans-serif; transition: border-color 0.3s ease; }
    input[type="text"]:focus, input[type="tel"]:focus, input[type="password"]:focus, select:focus { border-color: #263b58; outline: none; }
    button.submit-btn { margin-top: 20px; width: 100%; padding: 14px; background-color: #263b58; color: white; font-family: 'Poppins', sans-serif; font-weight: 700; font-size: 1.1rem; border: none; border-radius: 10px; cursor: pointer; transition: background-color 0.3s ease; }
    button.submit-btn:hover { background-color: #1b2c45; }
    table { width: 100%; border-collapse: collapse; margin-top: 40px; font-family: 'Open Sans', sans-serif; font-size: 1rem; }
    th, td { border: 1px solid #ddd; padding: 10px; text-align: center; }
    th { background-color: #263b58; color: white; font-weight: 700; }
    .success-message { color: green; text-align: center; margin-top: 18px; }
    .error-message { color: red; text-align: center; margin-top: 18px; }
  </style>
</head>
<body>

  <div class="sidebar">
    <div class="logo">
      <img src="../../static/images/K-Logo-on-black-t.png" alt="Khalaifat Logo" />
    </div>
    <div class="lang-switch">
      <button onclick="window.location.href='../en/employee_registration_en.html'">English</button>
    </div>
    <nav>
      <button onclick="alert('الصفحة الرئيسية')">الصفحة الرئيسية</button>
      <button onclick="alert('تسجيل تقرير مبيعات')">تسجيل تقرير مبيعات</button>
      <button onclick="alert('تسجيل موظفين')">تسجيل موظفين</button>
      <button onclick="alert('إدارة المنتجات')">إدارة المنتجات</button>
      <button onclick="alert('ملفي الشخصي')">ملفي الشخصي</button>
    </nav>
    <button class="logout-btn" onclick="alert('تم تسجيل الخروج')">تسجيل خروج</button>
  </div>

  <main>
    <header>تسجيل الموظفين - شركة خليفات</header>

    <form id="employeeForm">
      <label for="employeeName">اسم الموظف</label>
      <input type="text" id="employeeName" name="employeeName" placeholder="أدخل اسم الموظف" required />

      <label for="phoneNumber">رقم الهاتف</label>
      <input type="tel" id="phoneNumber" name="phoneNumber" placeholder="أدخل رقم الهاتف" required />

      <label for="password">كلمة المرور</label>
      <input type="password" id="password" name="password" placeholder="أدخل كلمة المرور" required />

      <label for="role">الدور</label>
      <select id="role" name="role" required>
        <option value="">اختر الدور</option>
        <option value="employee">موظف</option>
        <option value="admin">مدير</option>
        <option value="manager">مشرف</option>
      </select>

      <button type="submit" class="submit-btn">إضافة الموظف</button>
    </form>

    <table id="employeesTable">
      <thead>
        <tr>
          <th>اسم الموظف</th>
          <th>رقم الهاتف</th>
          <th>الدور</th>
        </tr>
      </thead>
      <tbody>
        <!-- سيتم تعبئة البيانات تلقائيًا -->
      </tbody>
    </table>

    <p class="success-message" id="successMessage" style="display:none;"></p>
    <p class="error-message" id="errorMessage" style="display:none;"></p>
  </main>

  <script>
    const API_URL = 'http://127.0.0.1:8000/users/';

    const employeeForm = document.getElementById('employeeForm');
    const employeesTableBody = document.querySelector('#employeesTable tbody');
    const successMessage = document.getElementById('successMessage');
    const errorMessage = document.getElementById('errorMessage');

    // جلب جميع المستخدمين من السيرفر عند فتح الصفحة
    async function fetchUsers() {
      employeesTableBody.innerHTML = `<tr><td colspan="3">جاري التحميل...</td></tr>`;
      try {
        const response = await fetch(API_URL);
        const users = await response.json();
        employeesTableBody.innerHTML = '';
        if (Array.isArray(users) && users.length > 0) {
          users.forEach(u => addEmployeeRow(u.name, getPhoneFromEmail(u.email), u.role));
        } else {
          employeesTableBody.innerHTML = `<tr><td colspan="3">لا يوجد موظفين بعد.</td></tr>`;
        }
      } catch (err) {
        employeesTableBody.innerHTML = `<tr><td colspan="3">تعذر جلب البيانات!</td></tr>`;
      }
    }

    // عند إرسال النموذج
    employeeForm.addEventListener('submit', async (e) => {
      e.preventDefault();

      const name = employeeForm.employeeName.value.trim();
      const phone = employeeForm.phoneNumber.value.trim();
      const password = employeeForm.password.value;
      const role = employeeForm.role.value;

      if (!name || !phone || !password || !role) {
        errorMessage.style.display = 'block';
        errorMessage.textContent = "يرجى تعبئة جميع الحقول.";
        setTimeout(() => errorMessage.style.display = 'none', 2000);
        return;
      }

      // سيستخدم رقم الهاتف كإيميل تجريبي
      const userData = {
        name: name,
        email: phone + "@khalaifat.local",
        password: password,
        role: role
      };

      try {
        const response = await fetch(API_URL, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(userData)
        });

        const result = await response.json();

        if (response.ok) {
          successMessage.textContent = "تم إضافة الموظف بنجاح!";
          successMessage.style.display = 'block';
          errorMessage.style.display = 'none';
          addEmployeeRow(result.name, phone, result.role);
          employeeForm.reset();
          setTimeout(() => successMessage.style.display = 'none', 2000);
        } else {
          errorMessage.textContent = result.detail || "حدث خطأ أثناء إضافة الموظف!";
          errorMessage.style.display = 'block';
          successMessage.style.display = 'none';
          setTimeout(() => errorMessage.style.display = 'none', 3000);
        }
      } catch (err) {
        errorMessage.textContent = "تعذر الاتصال بالخادم!";
        errorMessage.style.display = 'block';
        successMessage.style.display = 'none';
        setTimeout(() => errorMessage.style.display = 'none', 3000);
      }
    });

    // إضافة صف للجدول
    function addEmployeeRow(name, phone, role) {
      const tr = document.createElement('tr');
      tr.innerHTML = `
        <td>${name}</td>
        <td>${phone}</td>
        <td>${role === 'employee' ? 'موظف' : role === 'admin' ? 'مدير' : 'مشرف'}</td>
      `;
      employeesTableBody.appendChild(tr);
    }

    // استخراج رقم الهاتف من البريد (للعرض فقط)
    function getPhoneFromEmail(email) {
      if (email && email.includes('@')) {
        return email.split('@')[0];
      }
      return email;
    }

    // تحميل المستخدمين عند تحميل الصفحة
    window.onload = fetchUsers;
  </script>
</body>
</html>

import os

project_name = "khalaifat_app"

folders = [
    f"{project_name}/app/models",
    f"{project_name}/app/routes"
]

files = [
    f"{project_name}/.env",
    f"{project_name}/requirements.txt",
    f"{project_name}/app/__init__.py",
    f"{project_name}/app/main.py",
    f"{project_name}/app/config.py",
    f"{project_name}/app/db.py",
    f"{project_name}/app/models/__init__.py",
    f"{project_name}/app/models/user.py",
    f"{project_name}/app/models/product.py",
    f"{project_name}/app/models/report.py",
    f"{project_name}/app/routes/__init__.py",
    f"{project_name}/app/routes/user_routes.py",
    f"{project_name}/app/routes/product_routes.py",
    f"{project_name}/app/routes/report_routes.py",
]

for folder in folders:
    os.makedirs(folder, exist_ok=True)
    print(f"Created folder: {folder}")

for file in files:
    with open(file, "w", encoding="utf-8") as f:
        pass
    print(f"Created file: {file}")

<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Product Management - Khalaifat Company</title>

  <!-- Poppins & Open Sans fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans&family=Poppins:wght@400;700&display=swap" rel="stylesheet" />

  <style>
    body {
      font-family: 'Open Sans', sans-serif;
      background-color: #f9fafc;
      margin: 0;
      padding: 0;
      direction: ltr;
      color: #263b58;
      min-height: 100vh;
      display: flex;
    }
    .sidebar {
      width: 220px;
      background-color: #263b58;
      color: white;
      display: flex;
      flex-direction: column;
      padding-top: 20px;
      box-sizing: border-box;
      min-height: 100vh;
    }
    .sidebar .logo {
      text-align: center;
      margin-bottom: 30px;
      user-select: none;
    }
    .sidebar .logo img {
      max-width: 140px;
      height: auto;
      display: inline-block;
    }
    .sidebar nav {
      flex: 1;
    }
    .sidebar nav button {
      width: 100%;
      background: none;
      border: none;
      color: white;
      padding: 15px 20px;
      text-align: left;
      font-family: 'Poppins', sans-serif;
      font-weight: 600;
      font-size: 1rem;
      cursor: pointer;
      border-left: 4px solid transparent;
      transition: background-color 0.3s ease, border-color 0.3s ease;
    }
    .sidebar nav button:hover {
      background-color: #1b2c45;
      border-left-color: #dda986;
    }
    .logout-btn {
      background-color: #464646;
      color: white;
      border: none;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      font-size: 1rem;
      width: 90%;
      margin: 20px auto;
      transition: background-color 0.3s ease;
    }
    .logout-btn:hover {
      background-color: #263b58;
    }
    main {
      flex: 1;
      padding: 30px 50px;
      overflow-y: auto;
      background: white;
      box-sizing: border-box;
    }
    header {
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      font-size: 1.8rem;
      margin-bottom: 30px;
      color: #263b58;
      text-align: center;
    }
    form {
      max-width: 600px;
      margin: 0 auto 40px auto;
      font-family: 'Open Sans', sans-serif;
    }
    label {
      display: block;
      margin-bottom: 8px;
      margin-top: 20px;
      font-weight: 600;
      font-size: 1rem;
      color: #263b58;
    }
    input[type="text"],
    textarea {
      width: 100%;
      padding: 12px 15px;
      font-size: 1rem;
      border-radius: 8px;
      border: 1px solid #ccc;
      box-sizing: border-box;
      font-family: 'Open Sans', sans-serif;
      resize: vertical;
      transition: border-color 0.3s ease;
    }
    input[type="text"]:focus,
    textarea:focus {
      border-color: #263b58;
      outline: none;
    }
    textarea {
      min-height: 120px;
    }
    button.submit-btn {
      margin-top: 20px;
      width: 100%;
      padding: 14px;
      background-color: #263b58;
      color: white;
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      font-size: 1.1rem;
      border: none;
      border-radius: 10px;
      cursor: pointer;
      transition: background-color 0.3s ease;
    }
    button.submit-btn:hover {
      background-color: #1b2c45;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
      font-family: 'Open Sans', sans-serif;
      font-size: 1rem;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 10px;
      text-align: center;
      position: relative;
    }
    th {
      background-color: #263b58;
      color: white;
      font-weight: 700;
    }
    .btn-minus {
      color: white;
      background-color: #e74c3c;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      font-weight: 700;
      cursor: pointer;
      user-select: none;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      font-size: 18px;
      position: relative;
    }
    .actions-menu {
      display: none;
      position: absolute;
      top: 30px;
      right: 0;
      background: white;
      border: 1px solid #ccc;
      box-shadow: 0 2px 8px rgba(0,0,0,0.2);
      border-radius: 6px;
      z-index: 10;
      min-width: 140px;
      font-family: 'Open Sans', sans-serif;
    }
    .actions-menu button {
      display: block;
      width: 100%;
      padding: 10px 12px;
      background: none;
      border: none;
      text-align: left;
      cursor: pointer;
      font-size: 0.95rem;
      color: #263b58;
      font-weight: 600;
      transition: background-color 0.2s ease;
    }
    .actions-menu button:hover {
      background-color: #f0f0f0;
    }
    .show-menu {
      display: block;
    }
    #editProductModal {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      border-radius: 10px;
      box-shadow: 0 3px 15px rgba(0,0,0,0.3);
      padding: 25px 30px;
      z-index: 100;
      display: none;
      width: 90%;
      max-width: 500px;
      font-family: 'Open Sans', sans-serif;
    }
    #editProductModal label {
      margin-top: 15px;
      font-weight: 600;
      color: #263b58;
    }
    #editProductModal input,
    #editProductModal textarea {
      width: 100%;
      padding: 10px 12px;
      border-radius: 6px;
      border: 1px solid #ccc;
      margin-top: 5px;
      box-sizing: border-box;
      resize: vertical;
      font-family: 'Open Sans', sans-serif;
    }
    #editProductModal textarea {
      min-height: 80px;
    }
    #editProductModalButtons {
      margin-top: 20px;
      text-align: center;
    }
    #editProductModalButtons button {
      padding: 10px 20px;
      font-weight: 700;
      font-family: 'Poppins', sans-serif;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      margin: 0 10px;
      transition: background-color 0.3s ease;
      font-size: 1rem;
    }
    #saveEditBtn {
      background-color: #263b58;
      color: white;
    }
    #saveEditBtn:hover {
      background-color: #1b2c45;
    }
    #cancelEditBtn {
      background-color: #ccc;
      color: #263b58;
    }
    #cancelEditBtn:hover {
      background-color: #aaa;
    }
    #modalOverlay {
      display: none;
      position: fixed;
      top: 0; left: 0; right: 0; bottom: 0;
      background-color: rgba(0,0,0,0.4);
      z-index: 90;
    }
    #confirmDeleteModal {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      border-radius: 10px;
      box-shadow: 0 3px 15px rgba(0,0,0,0.3);
      padding: 25px 30px;
      z-index: 110;
      display: none;
      width: 90%;
      max-width: 400px;
      font-family: 'Open Sans', sans-serif;
      text-align: center;
    }
    #confirmDeleteModal p {
      font-size: 1.1rem;
      color: #263b58;
      margin-bottom: 25px;
      font-weight: 600;
    }
    #confirmDeleteButtons button {
      padding: 10px 25px;
      font-weight: 700;
      font-family: 'Poppins', sans-serif;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      margin: 0 15px;
      transition: background-color 0.3s ease;
      font-size: 1rem;
      min-width: 80px;
    }
    #confirmYesBtn {
      background-color: #e74c3c;
      color: white;
    }
    #confirmYesBtn:hover {
      background-color: #c0392b;
    }
    #confirmNoBtn {
      background-color: #ccc;
      color: #263b58;
    }
    #confirmNoBtn:hover {
      background-color: #aaa;
    }
  </style>
</head>
<body>

  <div class="sidebar">
    <div class="logo">
      <img src="K-transparent-black.png" alt="Khalaifat Logo" />
    </div>
    <nav>
      <button onclick="alert('Home')">Home</button>
      <button onclick="alert('Register Sales Report')">Register Sales Report</button>
      <button onclick="alert('Register Technician Report')">Register Technician Report</button>
      <button onclick="alert('Manage Products')">Manage Products</button>
      <button onclick="alert('My Profile')">My Profile</button>
    </nav>
    <button class="logout-btn" onclick="alert('Logged out')">Logout</button>
  </div>

  <main>
    <header>Manage Products - Khalaifat Company</header>

    <form id="productForm">
      <label for="productName">Product Name</label>
      <input type="text" id="productName" name="productName" placeholder="Enter product name" required />

      <label for="barcode">Barcode Number</label>
      <input type="text" id="barcode" name="barcode" placeholder="Enter barcode number" required />

      <label for="productDescription">Product Description</label>
      <textarea id="productDescription" name="productDescription" placeholder="Enter product description" required></textarea>

      <button type="submit" class="submit-btn">Add Product</button>
    </form>

    <table id="productsTable" style="margin-top: 40px;">
      <thead>
        <tr>
          <th>Product Name</th>
          <th>Barcode Number</th>
          <th>Product Description</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <!-- Added products appear here -->
      </tbody>
    </table>
  </main>

  <!-- Edit product modal -->
  <div id="modalOverlay"></div>
  <div id="editProductModal">
    <h3>Edit Product Details</h3>
    <form id="editProductForm">
      <label for="editProductName">Product Name</label>
      <input type="text" id="editProductName" name="editProductName" required />

      <label for="editBarcode">Barcode Number</label>
      <input type="text" id="editBarcode" name="editBarcode" required />

      <label for="editProductDescription">Product Description</label>
      <textarea id="editProductDescription" name="editProductDescription" required></textarea>

      <div id="editProductModalButtons">
        <button type="submit" id="saveEditBtn">Save Changes</button>
        <button type="button" id="cancelEditBtn">Cancel</button>
      </div>
    </form>
  </div>

  <!-- Delete confirmation modal -->
  <div id="confirmDeleteModal">
    <p>Are you sure you want to delete this data?</p>
    <div id="confirmDeleteButtons">
      <button id="confirmYesBtn">Yes</button>
      <button id="confirmNoBtn">No</button>
    </div>
  </div>

  <script>
    const productForm = document.getElementById('productForm');
    const productsTableBody = document.querySelector('#productsTable tbody');
    const modalOverlay = document.getElementById('modalOverlay');
    const editModal = document.getElementById('editProductModal');
    const editProductForm = document.getElementById('editProductForm');
    const confirmDeleteModal = document.getElementById('confirmDeleteModal');
    const confirmYesBtn = document.getElementById('confirmYesBtn');
    const confirmNoBtn = document.getElementById('confirmNoBtn');

    let products = [];
    let editIndex = null;
    let deleteIndex = null;

    function renderProducts() {
      productsTableBody.innerHTML = '';

      products.forEach((product, index) => {
        const tr = document.createElement('tr');

        tr.innerHTML = `
          <td>${product.name}</td>
          <td>${product.barcode}</td>
          <td>${product.description}</td>
          <td style="position: relative;">
            <span class="btn-minus" title="Options" data-index="${index}">−</span>
            <div class="actions-menu" id="actionsMenu${index}">
              <button type="button" class="edit-btn" data-index="${index}">Edit</button>
              <button type="button" class="delete-btn" data-index="${index}">Delete</button>
            </div>
          </td>
        `;

        productsTableBody.appendChild(tr);
      });

      // Bind option buttons
      document.querySelectorAll('.btn-minus').forEach(btn => {
        btn.onclick = (e) => {
          const idx = e.target.dataset.index;
          toggleMenu(idx);
        };
      });

      document.querySelectorAll('.edit-btn').forEach(btn => {
        btn.onclick = (e) => {
          const idx = e.target.dataset.index;
          openEditModal(idx);
        };
      });

      document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.onclick = (e) => {
          const idx = e.target.dataset.index;
          openDeleteConfirm(idx);
        };
      });
    }

    function toggleMenu(index) {
      // Close all open menus first
      document.querySelectorAll('.actions-menu').forEach(menu => {
        if(menu.id !== 'actionsMenu'+index) {
          menu.classList.remove('show-menu');
        }
      });

      // Toggle selected menu
      const menu = document.getElementById('actionsMenu' + index);
      menu.classList.toggle('show-menu');
    }

    function closeAllMenus() {
      document.querySelectorAll('.actions-menu').forEach(menu => {
        menu.classList.remove('show-menu');
      });
    }

    document.addEventListener('click', (e) => {
      if (!e.target.classList.contains('btn-minus') && !e.target.closest('.actions-menu')) {
        closeAllMenus();
      }
    });

    productForm.addEventListener('submit', (e) => {
      e.preventDefault();

      const name = productForm.productName.value.trim();
      const barcode = productForm.barcode.value.trim();
      const description = productForm.productDescription.value.trim();

      if(name && barcode && description) {
        products.push({name, barcode, description});
        renderProducts();
        productForm.reset();
      }
    });

    // Open edit modal
    function openEditModal(index) {
      editIndex = index;
      const product = products[index];

      editProductForm.editProductName.value = product.name;
      editProductForm.editBarcode.value = product.barcode;
      editProductForm.editProductDescription.value = product.description;

      editModal.style.display = 'block';
      modalOverlay.style.display = 'block';
      closeAllMenus();
    }

    // Close edit modal
    function closeEditModal() {
      editModal.style.display = 'none';
      modalOverlay.style.display = 'none';
      editIndex = null;
    }

    editProductForm.addEventListener('submit', (e) => {
      e.preventDefault();

      if(editIndex !== null) {
        products[editIndex].name = editProductForm.editProductName.value.trim();
        products[editIndex].barcode = editProductForm.editBarcode.value.trim();
        products[editIndex].description = editProductForm.editProductDescription.value.trim();

        renderProducts();
        closeEditModal();
      }
    });

    document.getElementById('cancelEditBtn').addEventListener('click', () => {
      closeEditModal();
    });

    // Open delete confirmation modal
    function openDeleteConfirm(index) {
      deleteIndex = index;
      confirmDeleteModal.style.display = 'block';
      modalOverlay.style.display = 'block';
      closeAllMenus();
    }

    // Close delete confirmation modal
    function closeDeleteConfirm() {
      confirmDeleteModal.style.display = 'none';
      modalOverlay.style.display = 'none';
      deleteIndex = null;
    }

    confirmYesBtn.addEventListener('click', () => {
      if(deleteIndex !== null) {
        products.splice(deleteIndex, 1);
        renderProducts();
        closeDeleteConfirm();
      }
    });

    confirmNoBtn.addEventListener('click', () => {
      closeDeleteConfirm();
    });
  </script>

</body>
</html>

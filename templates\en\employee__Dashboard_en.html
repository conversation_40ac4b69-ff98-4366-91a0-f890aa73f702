<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Employee Dashboard - Khalaifat Company</title>

  <!-- Poppins & Open Sans fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans&family=Poppins:wght@400;700&display=swap" rel="stylesheet" />

  <!-- Chart.js library -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

  <style>
    body {
      font-family: 'Open Sans', sans-serif;
      background-color: #f9fafc;
      margin: 0;
      padding: 0;
      direction: ltr;
      color: #263b58;
      min-height: 100vh;
      display: flex;
      overflow: hidden;
    }

    /* Sidebar */
    .sidebar {
      width: 220px;
      background-color: #263b58;
      color: white;
      display: flex;
      flex-direction: column;
      padding-top: 20px;
      box-sizing: border-box;
      transition: width 0.3s ease;
      position: relative;
      min-height: 100vh;
    }

    .sidebar.collapsed {
      width: 70px;
    }

    .sidebar .logo {
      text-align: center;
      margin-bottom: 30px;
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      font-size: 1.5rem;
      letter-spacing: 1px;
      cursor: default;
      user-select: none;
    }

    .sidebar .logo img {
      max-width: 140px;
      height: auto;
      display: block;
      margin: 0 auto 10px auto;
    }

    .sidebar.collapsed .logo {
      font-size: 1.2rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .sidebar nav {
      flex: 1;
    }

    .sidebar nav button {
      width: 100%;
      background: none;
      border: none;
      color: white;
      padding: 15px 20px;
      text-align: left;
      font-family: 'Poppins', sans-serif;
      font-weight: 600;
      font-size: 1rem;
      cursor: pointer;
      border-left: 4px solid transparent;
      transition: background-color 0.3s ease, border-color 0.3s ease;
    }

    .sidebar nav button:hover {
      background-color: #1b2c45;
      border-left-color: #dda986;
    }

    .sidebar nav button:focus {
      outline: none;
      background-color: #1b2c45;
      border-left-color: #dda986;
    }

    .logout-btn {
      background-color: #464646;
      color: white;
      border: none;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      font-size: 1rem;
      width: 90%;
      margin: 20px auto;
      transition: background-color 0.3s ease;
    }

    .logout-btn:hover {
      background-color: #263b58;
    }

    /* Main content */
    main {
      flex: 1;
      padding: 20px 40px;
      overflow-y: auto;
    }

    header {
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      font-size: 1.8rem;
      margin-bottom: 30px;
      color: #263b58;
    }

    .welcome {
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      font-size: 1.4rem;
      margin-bottom: 25px;
      color: #263b58;
    }

    .cards {
      display: flex;
      gap: 20px;
      flex-wrap: wrap;
      justify-content: center;
      margin-bottom: 40px;
    }

    .card {
      background: #263b58;
      flex: 1 1 220px;
      min-width: 220px;
      padding: 25px;
      border-radius: 10px;
      box-shadow: 0 3px 8px rgba(38, 59, 88, 0.3);
      text-align: center;
      color: white;
    }

    .card h3 {
      margin: 0 0 10px 0;
      font-family: 'Poppins', sans-serif;
    }

    .card p {
      font-size: 2rem;
      font-weight: 700;
      margin: 0;
    }

    .progress-chart-container {
      max-width: 320px;
      margin: 0 auto 40px auto;
      background: white;
      border-radius: 10px;
      padding: 20px;
      box-shadow: 0 3px 10px rgba(38,59,88,0.1);
      text-align: center;
    }

    .progress-chart-container h3 {
      font-family: 'Poppins', sans-serif;
      margin-bottom: 20px;
      color: #263b58;
    }

    .reports-section h2 {
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      margin-bottom: 20px;
      color: #263b58;
      text-align: center;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
    }

    th, td {
      border: 1px solid #ddd;
      padding: 10px;
      text-align: center;
      font-family: 'Open Sans', sans-serif;
    }

    th {
      background-color: #263b58;
      color: white;
      font-weight: 700;
    }

    .no-reports {
      font-style: italic;
      color: #888;
      text-align: center;
      margin-top: 20px;
    }
  </style>
</head>
<body>

  <div class="sidebar" id="sidebar">
    <div class="logo">
      <img src="K-transparent-black.png" alt="Khalaifat Logo" />
      Khalaifat
    </div>
    <nav>
      <button onclick="location.href='employee_dashboard.html'">Home</button>
      <button onclick="location.href='add_sales_report.html'">Add Sales Report</button>
      <button onclick="location.href='my_sales_reports.html'">My Sales Reports</button>
      <button onclick="location.href='profile.html'">My Profile</button>
    </nav>
    <button class="logout-btn" onclick="alert('Logged out (temporary)')">Logout</button>
  </div>

  <main>
    <header>Employee Dashboard - Khalaifat Company</header>

    <div class="welcome">Welcome, Mahdi</div>

    <div class="cards">
      <div class="card">
        <h3>Total Sales Today</h3>
        <p id="totalSalesToday">0 BHD</p>
      </div>
      <div class="card">
        <h3>Reports Count Monthly</h3>
        <p id="reportsCount">0</p>
      </div>
      <div class="card">
        <h3>Monthly Progress</h3>
        <p id="monthlyProgress">0%</p>
      </div>
    </div>

    <div class="progress-chart-container">
      <h3>Remaining to Achieve Monthly Goal</h3>
      <canvas id="progressChart" width="200" height="200"></canvas>
    </div>

    <section class="reports-section">
      <h2>My Daily Sales Reports</h2>

      <table>
        <thead>
          <tr>
            <th>Date</th>
            <th>Product Model</th>
            <th>Quantity</th>
            <th>Unit Price (BHD)</th>
            <th>Total (BHD)</th>
          </tr>
        </thead>
        <tbody id="reportsTableBody">
          <!-- Reports will be loaded here -->
        </tbody>
      </table>

      <p id="noReportsMessage" class="no-reports">No reports found.</p>
    </section>
  </main>

  <script>
    // Sample data for employee's sales reports
    const salesReports = [
      { date: "2025-05-20", model: "KGN57VL20M", quantity: 3, unitPrice: 100, total: 300 },
      { date: "2025-05-21", model: "MSM6700GB", quantity: 1, unitPrice: 350, total: 350 },
      { date: "2025-05-22", model: "MMB33P5BGB", quantity: 2, unitPrice: 150, total: 300 },
    ];

    // Calculate key performance indicators
    const totalSalesTodayValue = salesReports.reduce((sum, r) => sum + r.total, 0);
    const reportsCountValue = salesReports.length;
    const monthlyGoal = 2000;  // Monthly sales target
    const monthlyProgressValue = (totalSalesTodayValue / monthlyGoal) * 100;
    const remainingPercent = Math.max(0, 100 - monthlyProgressValue);

    document.getElementById('totalSalesToday').textContent = totalSalesTodayValue.toFixed(3) + " BHD";
    document.getElementById('reportsCount').textContent = reportsCountValue;
    document.getElementById('monthlyProgress').textContent = monthlyProgressValue.toFixed(1) + "%";

    const tableBody = document.getElementById('reportsTableBody');
    const noReportsMessage = document.getElementById('noReportsMessage');

    function loadReports() {
      if (salesReports.length === 0) {
        noReportsMessage.style.display = 'block';
        return;
      }

      noReportsMessage.style.display = 'none';

      salesReports.forEach(report => {
        const row = document.createElement('tr');

        row.innerHTML = `
          <td>${report.date}</td>
          <td>${report.model}</td>
          <td>${report.quantity}</td>
          <td>${report.unitPrice.toFixed(3)}</td>
          <td>${report.total.toFixed(3)}</td>
        `;

        tableBody.appendChild(row);
      });
    }

    loadReports();

    // Progress donut chart showing completion and remaining
    const ctx = document.getElementById('progressChart').getContext('2d');
    const progressChart = new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: ['Completed', 'Remaining'],
        datasets: [{
          data: [monthlyProgressValue, remainingPercent],
          backgroundColor: ['#263b58', '#dda986'],
          borderWidth: 0
        }]
      },
      options: {
        cutout: '70%',
        responsive: true,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              font: { family: 'Poppins', weight: '700' },
              color: '#263b58'
            }
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                return context.label + ': ' + context.parsed.toFixed(1) + '%';
              }
            }
          }
        }
      }
    });
  </script>

</body>
</html>

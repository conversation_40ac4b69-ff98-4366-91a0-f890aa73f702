from fastapi import APIRouter
from typing import List
from app.db import products_collection
from app.models.product import ProductModel, ProductCreate

router = APIRouter()

@router.post("/", response_model=ProductModel)
async def create_product(product: ProductCreate):
    product_dict = product.dict()
    result = await products_collection.insert_one(product_dict)
    created_product = await products_collection.find_one({"_id": result.inserted_id})
    return created_product

@router.get("/", response_model=List[ProductModel])
async def get_products():
    products = []
    async for product in products_collection.find():
        products.append(ProductModel(**product))
    return products

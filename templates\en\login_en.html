<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Login - Khalaifat Company</title>

  <!-- Poppins & Open Sans fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans&family=Poppins:wght@400;700&display=swap" rel="stylesheet" />

  <style>
    body {
      font-family: 'Open Sans', sans-serif;
      background-color: #263b58; /* midnight blue */
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      direction: ltr;
      color: white;
    }

    .login-container {
      background-color: #fff;
      color: #263b58;
      width: 350px;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 8px 16px rgba(0,0,0,0.3);
      text-align: center;
    }

    .login-container img {
      width: 160px;
      margin-bottom: 25px;
    }

    h2 {
      font-family: 'Poppins', sans-serif;
      margin-bottom: 20px;
      font-weight: 700;
    }

    input[type="email"],
    input[type="password"] {
      width: 100%;
      padding: 12px;
      margin: 12px 0 20px 0;
      border: 1px solid #ccc;
      border-radius: 6px;
      font-size: 1rem;
      font-family: 'Open Sans', sans-serif;
    }

    button {
      width: 100%;
      background-color: #263b58;
      color: white;
      border: none;
      padding: 14px;
      font-size: 1.1rem;
      border-radius: 8px;
      cursor: pointer;
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      transition: background-color 0.3s ease;
    }

    button:hover {
      background-color: #1b2c45;
    }

    .forgot-password {
      margin-top: 15px;
      font-size: 0.9rem;
      color: #555;
      cursor: pointer;
      text-decoration: underline;
    }
  </style>
</head>
<body>

  <div class="login-container">
    <!-- ضع رابط شعار الشركة هنا -->
    <img src="logo.jpg" alt="Khalaifat Logo" />

    <h2>Login</h2>

    <form id="loginForm">
      <input type="email" id="email" name="email" placeholder="Email Address" required />
      <input type="password" id="password" name="password" placeholder="Password" required />
      <button type="submit">Sign In</button>
    </form>

    <div class="forgot-password" onclick="alert('Password recovery is not enabled yet.')">Forgot Password?</div>
  </div>

  <script>
    const form = document.getElementById('loginForm');
    form.addEventListener('submit', e => {
      e.preventDefault();
      alert('Logged in (temporarily)');
      form.reset();
    });
  </script>

</body>
</html>

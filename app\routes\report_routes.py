from fastapi import APIRouter
from typing import List
from app.db import reports_collection
from app.models.report import ReportModel, ReportCreate

router = APIRouter()

@router.post("/", response_model=ReportModel)
async def create_report(report: ReportCreate):
    report_dict = report.dict()
    result = await reports_collection.insert_one(report_dict)
    created_report = await reports_collection.find_one({"_id": result.inserted_id})
    return created_report

@router.get("/", response_model=List[ReportModel])
async def get_reports():
    reports = []
    async for report in reports_collection.find():
        reports.append(ReportModel(**report))
    return reports

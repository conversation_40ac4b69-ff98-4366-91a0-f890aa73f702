# تطبيق خليفات

تطبيق ويب مبني باستخدام Flask لإدارة المبيعات والتقارير الفنية.

## المتطلبات

- Python 3.7+
- Flask
- Flask-SQLAlchemy
- Gunicorn (للنشر)

## التثبيت

1. قم بتثبيت المتطلبات:
```
pip install -r requirements.txt
```

2. قم بتشغيل التطبيق محلياً:
```
python app.py
```

## النشر على Render.com

1. قم بإنشاء حساب على [Render.com](https://render.com/)
2. قم بربط مستودع Git الخاص بك
3. قم بإنشاء خدمة ويب جديدة واختر مستودع المشروع
4. استخدم الإعدادات التالية:
   - نوع الخدمة: Web Service
   - البيئة: Python
   - أمر البناء: `pip install -r requirements.txt`
   - أمر البدء: `gunicorn app:app`

## هيكل المشروع

- `app.py`: ملف التطبيق الرئيسي
- `templates/`: قوالب HTML
- `static/`: الملفات الثابتة (CSS، JS، الصور)
- `requirements.txt`: متطلبات Python
- `Procfile`: ملف تكوين Render.com
